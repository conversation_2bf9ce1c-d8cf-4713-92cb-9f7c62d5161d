from game_backend.celery import app
from django.utils import timezone
from datetime import timedelta
from account.models.member import Member
from game.models import JobDone
from utils.utils import send_email
from .models import GameServer, MODE_FREERIDE, MODE_RACE, OnlineStatistics, STATE_LOBBY


app.conf.beat_schedule = {
    'dislocate': {
        'task': 'game.tasks.kill_dead_game_servers',
        'schedule': 60,
        'args': ()
    },
    'online_statistics': {
        'task': 'game.tasks.online_statistics',
        'schedule': 60,
        'args': ()
    },
    'livekit_room_count': {
       'task': 'audio_stream.tasks.livekit_room_count',
       'schedule': 60,
       'args': ()
    },
    'verify_zarinpal_zombies': {
        'task': 'shop.tasks.verify_zarinpal_zombies',
        'schedule': 300,
        'args': ()
    },
    # 'apply_market_purchases': {
    #     'task': 'shop.tasks.apply_market_purchases',
    #     'schedule': 60,
    #     'args': ()
    # },
    'police_ban': {
        'task': 'game.tasks.check_police_ban',
        'schedule': 3600 * 24,
        'args': ()
    },
}


@app.task
def kill_dead_game_servers():
    THRESHOLD = 60
    deads = GameServer.objects.filter(last_update__lt=(timezone.now() - timedelta(seconds=THRESHOLD)))
    for dead in deads:
        dead.is_alive = False
        dead.save()
    
@app.task
def online_statistics():
    max_freeride_user = 0
    max_race_user = 0
    statistics = OnlineStatistics()
    
    #RaceMode
    race_servers = GameServer.objects.filter(mode=MODE_RACE, is_alive=True, is_test=False, hide=False)
    statistics.number_of_race_servers = len(race_servers)
    for server in race_servers:
        if server.real_players == 0 and server.state == STATE_LOBBY:
            statistics.free_race_servers += 1
        statistics.number_of_race_users += server.real_players
        statistics.max_race_server_user = max(statistics.max_race_server_user, server.real_players)
    

    #FreeRideMode
    freeride_servers = GameServer.objects.filter(mode=MODE_FREERIDE, is_alive=True, is_test=False, hide=False)
    statistics.number_of_freeride_servers = len(freeride_servers)
    for server in freeride_servers:
        statistics.number_of_freeride_users += server.real_players
        statistics.max_freeride_server_user = max(statistics.max_freeride_server_user, server.real_players)
    
    statistics.total_online_players = statistics.number_of_freeride_users + statistics.number_of_race_users
    statistics.save()
    if statistics.free_race_servers == 0 and len(race_servers) < 5:
        send_email.delay("<EMAIL>", "No Race Server Available", "Total Players: {}\nNumber of Freeride Users: {}\nNumber of Race Users: {}\nNumber of Race Servers: {}\n"
                         .format(statistics.total_online_players, statistics.number_of_freeride_users,
                                  statistics.number_of_race_users, statistics.number_of_race_servers))


@app.task
def check_police_ban():
    BAN_TIME = 3600 * 24 * 7
    JOB_DONE_THRESHOLD = 100
    TIME_DELTA = timedelta(hours=48)
    COIN_PENALTY = 200

    # member.add_ban(seconds, False)

    polices = Member.objects.filter(police_job_enabled=True)

    message = ""

    ban_count = 0
    member:Member
    for member in polices:
        if member.is_ban:
            continue
        count = JobDone.objects.filter(member=member, job="policeReport").filter(created__gt=timezone.now() - TIME_DELTA).count()
        if count > JOB_DONE_THRESHOLD:
            ban_count += 1
            penalty = count * COIN_PENALTY
            message += str(ban_count) + ": -" + str(penalty) + " :" + member.username + " " + member.handle + " " + member.title + "\n"
            member.coin -= penalty
            if member.coin < 0:
                member.coin = 0
            member.save()
            member.add_ban(BAN_TIME, True)
            JobDone.objects.filter(member=member, job="policeReport").delete()


    send_email.delay("<EMAIL>", "Police Ban Job", message)
