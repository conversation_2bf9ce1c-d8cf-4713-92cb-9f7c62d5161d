import json
import logging
from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.utils import timezone
from django.db import transaction

from account.models import Member
from .models import (
    PropHuntGame, PropHuntParticipant, PropHuntSeason,
    BattlePassTier, BattlePassReward, BattlePassProgress, ClaimedReward
)
from .serializers import (
    PropHuntGameSerializer, PropHuntParticipantSerializer,
    BattlePassProgressSerializer, BattlePassRewardSerializer
)

logger = logging.getLogger('django')


@api_view(['POST'])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def start_prophunt_game(request):
    """Start a new PropHunt game"""
    data = request.data

    server_id = data.get("server_id")
    if not server_id:
        return JsonResponse(status=400, data={"message": "server_id is required"})

    players = data.get("players", [])
    if len(players) < 2:
        return JsonResponse(status=400, data={"message": "At least 2 players required"})

    try:
        with transaction.atomic():
            # Create the game
            game = PropHuntGame.objects.create(
                server_id=server_id,
                max_players=data.get("max_players", 8),
                round_duration=data.get("round_duration", 300),
                status='waiting'
            )

            # Add participants
            for player_data in players:
                member_id = player_data.get("member_id")
                team = player_data.get("team")

                if not member_id or not team:
                    continue

                try:
                    member = Member.objects.get(id=member_id)
                    PropHuntParticipant.objects.create(
                        game=game,
                        member=member,
                        team=team
                    )
                except Member.DoesNotExist:
                    logger.warning(f"Member {member_id} not found for PropHunt game")
                    continue

            # Start the game
            game.start_game()

            return JsonResponse(status=200, data={
                "message": "PropHunt game started successfully",
                "game_id": game.id,
                "game": PropHuntGameSerializer(game).data
            })

    except Exception as e:
        logger.error(f"Error starting PropHunt game: {str(e)}")
        return JsonResponse(status=500, data={"message": "Failed to start game"})


@api_view(['POST'])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def end_prophunt_game(request):
    """End a PropHunt game and calculate rewards"""
    data = request.data

    game_id = data.get("game_id")
    if not game_id:
        return JsonResponse(status=400, data={"message": "game_id is required"})

    try:
        game = PropHuntGame.objects.get(id=game_id, status='active')
    except PropHuntGame.DoesNotExist:
        return JsonResponse(status=404, data={"message": "Active game not found"})

    winning_team = data.get("winning_team")
    player_stats = data.get("player_stats", [])

    try:
        with transaction.atomic():
            # Update participant statistics
            for stat in player_stats:
                member_id = stat.get("member_id")
                kills = stat.get("kills", 0)
                deaths = stat.get("deaths", 0)
                survived = stat.get("survived", False)

                try:
                    participant = PropHuntParticipant.objects.get(
                        game=game,
                        member_id=member_id
                    )
                    participant.kills = kills
                    participant.deaths = deaths
                    participant.survived = survived
                    participant.save()
                except PropHuntParticipant.DoesNotExist:
                    logger.warning(f"Participant {member_id} not found in game {game_id}")
                    continue

            # End the game (this will calculate and distribute rewards)
            game.end_game(winning_team=winning_team)

            # Update battle pass progress for all participants
            current_season = PropHuntSeason.get_current_season()
            if current_season:
                for participant in game.participants.all():
                    progress, created = BattlePassProgress.objects.get_or_create(
                        member=participant.member,
                        season=current_season
                    )
                    progress.update_tier()

            return JsonResponse(status=200, data={
                "message": "PropHunt game ended successfully",
                "game": PropHuntGameSerializer(game).data,
                "participants": PropHuntParticipantSerializer(game.participants.all(), many=True).data
            })

    except Exception as e:
        logger.error(f"Error ending PropHunt game: {str(e)}")
        return JsonResponse(status=500, data={"message": "Failed to end game"})


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def get_battlepass_progress(request):
    """Get current battle pass progress for the authenticated user"""
    member = request.user

    current_season = PropHuntSeason.get_current_season()
    if not current_season:
        return JsonResponse(status=404, data={"message": "No active season found"})

    # Get or create progress
    progress, created = BattlePassProgress.objects.get_or_create(
        member=member,
        season=current_season
    )

    # Update tier based on current season smart
    progress.update_tier()

    # Get available rewards for current tier
    available_tiers = BattlePassTier.objects.filter(
        season=current_season,
        tier_number__lte=progress.current_tier
    ).prefetch_related('rewards')

    # Get claimed rewards
    claimed_reward_ids = ClaimedReward.objects.filter(
        member=member,
        reward__tier__season=current_season
    ).values_list('reward_id', flat=True)

    tier_data = []
    for tier in available_tiers:
        tier_rewards = []
        for reward in tier.rewards.all():
            reward_data = BattlePassRewardSerializer(reward).data
            reward_data['claimed'] = reward.id in claimed_reward_ids
            reward_data['can_claim'] = (
                not reward_data['claimed'] and
                (reward.tier_type == 'free' or member.season_premium)
            )
            tier_rewards.append(reward_data)

        tier_data.append({
            'tier_number': tier.tier_number,
            'smart_required': tier.smart_required,
            'rewards': tier_rewards
        })

    return JsonResponse(status=200, data={
        "season": {
            "name": current_season.name,
            "description": current_season.description,
            "max_tier": current_season.max_tier,
            "smart_per_tier": current_season.smart_per_tier
        },
        "progress": BattlePassProgressSerializer(progress).data,
        "member_season_smart": member.season_smart,
        "member_season_premium": member.season_premium,
        "tiers": tier_data
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def claim_battlepass_reward(request):
    """Claim a battle pass reward"""
    data = request.data
    member = request.user

    reward_id = data.get("reward_id")
    if not reward_id:
        return JsonResponse(status=400, data={"message": "reward_id is required"})

    try:
        reward = BattlePassReward.objects.get(id=reward_id)
    except BattlePassReward.DoesNotExist:
        return JsonResponse(status=404, data={"message": "Reward not found"})

    # Check if reward is already claimed
    if ClaimedReward.objects.filter(member=member, reward=reward).exists():
        return JsonResponse(status=400, data={"message": "Reward already claimed"})

    # Get current season progress
    current_season = PropHuntSeason.get_current_season()
    if not current_season or reward.tier.season != current_season:
        return JsonResponse(status=400, data={"message": "Reward not available in current season"})

    progress, created = BattlePassProgress.objects.get_or_create(
        member=member,
        season=current_season
    )
    progress.update_tier()

    # Check if player has reached the required tier
    if progress.current_tier < reward.tier.tier_number:
        return JsonResponse(status=400, data={"message": "Tier not reached yet"})

    # Check if player has premium access for premium rewards
    if reward.tier_type == 'premium' and not member.season_premium:
        return JsonResponse(status=400, data={"message": "Premium battle pass required"})

    try:
        with transaction.atomic():
            # Claim the reward
            ClaimedReward.objects.create(member=member, reward=reward)

            # Apply reward to member
            if reward.reward_type == 'coin' and reward.coin_amount > 0:
                member.coin += reward.coin_amount
                member.save()

            # Log the reward claim
            member.log_data = member.log_data + f"battlepass_reward_claimed: {reward.name} (Tier {reward.tier.tier_number})\n"
            member.save()

            return JsonResponse(status=200, data={
                "message": "Reward claimed successfully",
                "reward": BattlePassRewardSerializer(reward).data
            })

    except Exception as e:
        logger.error(f"Error claiming reward: {str(e)}")
        return JsonResponse(status=500, data={"message": "Failed to claim reward"})
