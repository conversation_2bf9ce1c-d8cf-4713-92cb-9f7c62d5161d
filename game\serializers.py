from rest_framework import serializers
from .models import GameServer, Map, GameJoined, GameSettings, CoinTransfer

class GameSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = GameSettings
        fields = ("id", "player_join_add_time", "game_time", "lobby_time", "result_time", "countdown_time", "prophaunt_round_time", "prophaunt_lobby_time")


class GameServerSerializer(serializers.ModelSerializer):
    class Meta:
        model = GameServer
        fields = ("id", "ip", "port", "is_test", "current_stage", "total_stage", "server_key", "gun_allow")


#For Using in freeride list
class DetailedGameServerSerializer(serializers.ModelSerializer):
    class Meta:
        model = GameServer
        fields = ("id", "ip", "port", "is_test", "players_joined", "max_players_start", "name", "has_password", "password", "gun_allow")


class MapSerializer(serializers.ModelSerializer):
    class Meta:
        model = Map
        fields = ("id", "name", "path", "min_players_count", "max_players_count", "qualified_count", "bot_count", "bot_wait_remove", "bot_reset_time", "image_path", "difficulty")


class GameJoinedSerializer(serializers.ModelSerializer):
    class Meta:
        model = GameJoined
        fields = ("id", "handle", "member_id", "rank", "cup", "coin", "crown")


class CoinTransferSerializer(serializers.ModelSerializer):
    class Meta:
        model = CoinTransfer
        fields = ("id", "from_handle_small", "to_handle_small", "amount")
