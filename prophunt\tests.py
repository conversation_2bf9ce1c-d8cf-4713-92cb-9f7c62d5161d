import json
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from rest_framework.test import APITestCase
from rest_framework import status

from account.models import Member
from .models import (
    PropHuntGame, PropHuntParticipant, PropHuntSeason,
    BattlePassTier, BattlePassReward, BattlePassProgress, ClaimedReward
)


class PropHuntModelTestCase(TestCase):
    """Test PropHunt models"""

    def setUp(self):
        """Set up test data"""
        self.member1 = Member.objects.create(
            username="player1",
            handle="Player1",
            email="<EMAIL>",
            coin=100,
            smart=50
        )
        self.member2 = Member.objects.create(
            username="player2",
            handle="Player2",
            email="<EMAIL>",
            coin=100,
            smart=50
        )

        self.game = PropHuntGame.objects.create(
            server_id="test_server_1",
            max_players=8,
            round_duration=300
        )

    def test_prophunt_game_creation(self):
        """Test PropHunt game creation"""
        self.assertEqual(self.game.status, 'waiting')
        self.assertEqual(self.game.server_id, 'test_server_1')
        self.assertEqual(self.game.max_players, 8)
        self.assertIsNone(self.game.start_time)

    def test_game_start(self):
        """Test starting a game"""
        self.game.start_game()
        self.assertEqual(self.game.status, 'active')
        self.assertIsNotNone(self.game.start_time)

    def test_game_end_with_rewards(self):
        """Test ending a game and calculating rewards"""
        # Add participants
        participant1 = PropHuntParticipant.objects.create(
            game=self.game,
            member=self.member1,
            team='props',
            kills=0,
            deaths=1,
            survived=False
        )
        participant2 = PropHuntParticipant.objects.create(
            game=self.game,
            member=self.member2,
            team='hunters',
            kills=1,
            deaths=0,
            survived=True
        )

        # Start and end game
        self.game.start_game()
        initial_coin1 = self.member1.coin
        initial_smart1 = self.member1.smart
        initial_coin2 = self.member2.coin
        initial_smart2 = self.member2.smart

        self.game.end_game(winning_team='hunters')

        # Refresh from database
        self.member1.refresh_from_db()
        self.member2.refresh_from_db()
        participant1.refresh_from_db()
        participant2.refresh_from_db()

        # Check game status
        self.assertEqual(self.game.status, 'finished')
        self.assertEqual(self.game.winning_team, 'hunters')
        self.assertIsNotNone(self.game.end_time)

        # Check rewards were calculated
        self.assertGreater(participant1.coin_earned, 0)
        self.assertGreater(participant1.smart_earned, 0)
        self.assertGreater(participant2.coin_earned, participant1.coin_earned)  # Winner bonus
        self.assertGreater(participant2.smart_earned, participant1.smart_earned)

        # Check rewards were applied to members
        self.assertEqual(self.member1.coin, initial_coin1 + participant1.coin_earned)
        self.assertEqual(self.member1.smart, initial_smart1 + participant1.smart_earned)
        self.assertEqual(self.member2.coin, initial_coin2 + participant2.coin_earned)
        self.assertEqual(self.member2.smart, initial_smart2 + participant2.smart_earned)

    def test_participant_creation(self):
        """Test participant creation"""
        participant = PropHuntParticipant.objects.create(
            game=self.game,
            member=self.member1,
            team='props'
        )

        self.assertEqual(participant.game, self.game)
        self.assertEqual(participant.member, self.member1)
        self.assertEqual(participant.team, 'props')
        self.assertEqual(participant.kills, 0)
        self.assertEqual(participant.deaths, 0)
        self.assertFalse(participant.survived)


class BattlePassModelTestCase(TestCase):
    """Test Battle Pass models"""

    def setUp(self):
        """Set up test data"""
        self.member = Member.objects.create(
            username="testplayer",
            handle="TestPlayer",
            email="<EMAIL>",
            coin=100,
            smart=50,
            season_smart=150
        )

        self.season = PropHuntSeason.objects.create(
            name="Season 1",
            description="First season",
            start_date=timezone.now() - timedelta(days=30),
            end_date=timezone.now() + timedelta(days=30),
            is_active=True,
            max_tier=100,
            smart_per_tier=100
        )

        self.tier1 = BattlePassTier.objects.create(
            season=self.season,
            tier_number=1,
            smart_required=100
        )

        self.tier2 = BattlePassTier.objects.create(
            season=self.season,
            tier_number=2,
            smart_required=200
        )

        self.free_reward = BattlePassReward.objects.create(
            tier=self.tier1,
            tier_type='free',
            reward_type='coin',
            name='100 Coins',
            coin_amount=100
        )

        self.premium_reward = BattlePassReward.objects.create(
            tier=self.tier1,
            tier_type='premium',
            reward_type='character',
            name='Special Character',
            item_id='char_001'
        )

    def test_season_creation(self):
        """Test season creation"""
        self.assertEqual(self.season.name, "Season 1")
        self.assertTrue(self.season.is_active)
        self.assertEqual(self.season.max_tier, 100)
        self.assertEqual(self.season.smart_per_tier, 100)

    def test_get_current_season(self):
        """Test getting current active season"""
        current = PropHuntSeason.get_current_season()
        self.assertEqual(current, self.season)

    def test_season_activation(self):
        """Test season activation"""
        # Create another season
        season2 = PropHuntSeason.objects.create(
            name="Season 2",
            description="Second season",
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=60),
            is_active=False
        )

        # Activate season 2
        season2.activate()

        # Refresh from database
        self.season.refresh_from_db()
        season2.refresh_from_db()

        # Check that season 1 is deactivated and season 2 is activated
        self.assertFalse(self.season.is_active)
        self.assertTrue(season2.is_active)

    def test_battle_pass_progress(self):
        """Test battle pass progress calculation"""
        progress = BattlePassProgress.objects.create(
            member=self.member,
            season=self.season,
            current_tier=1
        )

        # Update tier based on season smart (150 smart = tier 1, since tier 2 requires 200)
        new_tier = progress.update_tier()
        self.assertEqual(new_tier, 1)
        self.assertEqual(progress.current_tier, 1)

    def test_reward_claiming(self):
        """Test reward claiming"""
        progress = BattlePassProgress.objects.create(
            member=self.member,
            season=self.season,
            current_tier=1
        )

        # Claim free reward
        claimed = ClaimedReward.objects.create(
            member=self.member,
            reward=self.free_reward
        )

        self.assertEqual(claimed.member, self.member)
        self.assertEqual(claimed.reward, self.free_reward)
        self.assertIsNotNone(claimed.claimed_at)


class PropHuntAPITestCase(APITestCase):
    """Test PropHunt API endpoints"""

    def setUp(self):
        """Set up test data"""
        self.member1 = Member.objects.create(
            username="player1",
            handle="Player1",
            email="<EMAIL>",
            coin=100,
            smart=50
        )
        self.member2 = Member.objects.create(
            username="player2",
            handle="Player2",
            email="<EMAIL>",
            coin=100,
            smart=50
        )

        self.season = PropHuntSeason.objects.create(
            name="Test Season",
            description="Test season",
            start_date=timezone.now() - timedelta(days=30),
            end_date=timezone.now() + timedelta(days=30),
            is_active=True,
            max_tier=10,
            smart_per_tier=50
        )

        self.tier1 = BattlePassTier.objects.create(
            season=self.season,
            tier_number=1,
            smart_required=50
        )

        self.reward = BattlePassReward.objects.create(
            tier=self.tier1,
            tier_type='free',
            reward_type='coin',
            name='50 Coins',
            coin_amount=50
        )

    def test_start_prophunt_game(self):
        """Test starting a PropHunt game"""
        url = reverse('start_prophunt_game')
        data = {
            'server_id': 'test_server_1',
            'max_players': 8,
            'round_duration': 300,
            'players': [
                {'member_id': self.member1.id, 'team': 'props'},
                {'member_id': self.member2.id, 'team': 'hunters'}
            ]
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()
        self.assertIn('game_id', response_data)
        self.assertIn('game', response_data)

        # Check game was created
        game = PropHuntGame.objects.get(id=response_data['game_id'])
        self.assertEqual(game.server_id, 'test_server_1')
        self.assertEqual(game.status, 'active')
        self.assertEqual(game.participants.count(), 2)

    def test_end_prophunt_game(self):
        """Test ending a PropHunt game"""
        # Create a game first
        game = PropHuntGame.objects.create(
            server_id='test_server_1',
            status='active',
            start_time=timezone.now()
        )

        PropHuntParticipant.objects.create(
            game=game,
            member=self.member1,
            team='props'
        )
        PropHuntParticipant.objects.create(
            game=game,
            member=self.member2,
            team='hunters'
        )

        url = reverse('end_prophunt_game')
        data = {
            'game_id': game.id,
            'winning_team': 'hunters',
            'player_stats': [
                {'member_id': self.member1.id, 'kills': 0, 'deaths': 1, 'survived': False},
                {'member_id': self.member2.id, 'kills': 1, 'deaths': 0, 'survived': True}
            ]
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check game was ended
        game.refresh_from_db()
        self.assertEqual(game.status, 'finished')
        self.assertEqual(game.winning_team, 'hunters')

        # Check participants were updated
        participant1 = game.participants.get(member=self.member1)
        participant2 = game.participants.get(member=self.member2)

        self.assertEqual(participant1.kills, 0)
        self.assertEqual(participant1.deaths, 1)
        self.assertFalse(participant1.survived)

        self.assertEqual(participant2.kills, 1)
        self.assertEqual(participant2.deaths, 0)
        self.assertTrue(participant2.survived)

    def test_get_battlepass_progress(self):
        """Test getting battle pass progress"""
        self.client.force_authenticate(user=self.member1)

        url = reverse('get_battlepass_progress')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()
        self.assertIn('season', response_data)
        self.assertIn('progress', response_data)
        self.assertIn('tiers', response_data)
        self.assertEqual(response_data['season']['name'], 'Test Season')

    def test_claim_battlepass_reward(self):
        """Test claiming a battle pass reward"""
        self.client.force_authenticate(user=self.member1)

        # Set member to have enough season smart
        self.member1.season_smart = 100
        self.member1.save()

        url = reverse('claim_battlepass_reward')
        data = {'reward_id': self.reward.id}

        initial_coin = self.member1.coin

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check reward was claimed
        self.assertTrue(
            ClaimedReward.objects.filter(
                member=self.member1,
                reward=self.reward
            ).exists()
        )

        # Check coin was added
        self.member1.refresh_from_db()
        self.assertEqual(self.member1.coin, initial_coin + self.reward.coin_amount)

    def test_claim_reward_insufficient_tier(self):
        """Test claiming reward without sufficient tier"""
        self.client.force_authenticate(user=self.member1)

        # Member has 0 season smart, can't reach tier 1
        self.member1.season_smart = 0
        self.member1.save()

        url = reverse('claim_battlepass_reward')
        data = {'reward_id': self.reward.id}

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Tier not reached', response.json()['message'])
